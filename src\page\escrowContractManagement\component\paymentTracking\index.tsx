import React, { useState, useMemo, useEffect } from 'react';
import { Typography, Checkbox, Space, Modal, Col, Row } from 'antd';
import { ExclamationCircleFilled } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { formatNumber } from '../../../../utilities/regex';
import { Installment } from '../../../../types/depositContract';
import './styles.scss';

const { confirm } = Modal;
import TableComponent from '../../../../components/table';
import { useParams } from 'react-router-dom';
import { updatePaymentTracking } from '../../../../service/depositContract';
import { useUpdateField } from '../../../../hooks';

const { Title, Text } = Typography;

interface PaymentInstallment {
  id: string;
  installmentName: string;
  dueDate: string;
  amount: number;
  transactionSuccessful: boolean;
}

interface PaymentTrackingProps {
  installments?: Installment[];
  totalAmount?: number;
  paidAmount?: number;
}

const PaymentTracking: React.FC<PaymentTrackingProps> = ({ installments = [], totalAmount = 0, paidAmount = 0 }) => {
  const { id } = useParams();
  const [localInstallments, setLocalInstallments] = useState<Installment[]>(installments);

  const { mutateAsync: _updatePaymentTracking } = useUpdateField<any>({
    keyOfListQuery: ['get-deposit-contract'],
    keyOfDetailQuery: ['get-detail-deposit-contract'],
    apiQuery: updatePaymentTracking,
    isMessageError: false,
    messageSuccess: 'Chỉnh sửa hợp đồng ký quỹ thành công',
  });

  // Sync localInstallments với props installments
  useEffect(() => {
    setLocalInstallments(installments);
  }, [installments]);

  // Transform Installment data to PaymentInstallment format
  const transformInstallments = (installments: Installment[]): PaymentInstallment[] => {
    return installments.map((item, index) => {
      // Tính toán ngày đến hạn dự kiến từ expiredDays
      const calculateDueDate = (expiredDays: number, expiredDateType: string) => {
        const today = new Date();
        if (expiredDateType === 'numberDay' && expiredDays) {
          const dueDate = new Date(today);
          dueDate.setDate(today.getDate() + expiredDays);
          return dueDate.toLocaleDateString('vi-VN');
        }
        return '';
      };

      return {
        id: item.id || `${index + 1}`,
        installmentName: item.name || '',
        dueDate: calculateDueDate(item.expiredDays || 0, item.expiredDateType || 'numberDay'),
        amount: item.value || 0,
        transactionSuccessful: item.transactionSuccessful || false,
      };
    });
  };

  // Sử dụng localInstallments để có thể update local state
  const currentInstallments = localInstallments.length > 0 ? localInstallments : installments;
  const dataSource = currentInstallments.length > 0 ? transformInstallments(currentInstallments) : [];

  // Tính toán tổng tiền
  const calculatedTotalAmount = useMemo(() => {
    return totalAmount || dataSource.reduce((sum, item) => sum + item.amount, 0);
  }, [totalAmount, dataSource]);

  const calculatedPaidAmount = useMemo(() => {
    return (
      paidAmount || dataSource.filter(item => item.transactionSuccessful).reduce((sum, item) => sum + item.amount, 0)
    );
  }, [paidAmount, dataSource]);

  // Handler cho checkbox change
  const handleCheckboxChange = (installmentId: string, newStatus: boolean) => {
    confirm({
      title: 'Xác nhận giao dịch',
      icon: <ExclamationCircleFilled />,
      content:
        'Hành động này sẽ ghi nhận số tiền đã thu tương ứng với đợt vào tổng GT đã thanh toán và không được chỉnh sửa',
      okText: 'Xác nhận',
      cancelText: 'Hủy',
      centered: true,
      async onOk() {
        try {
          // Gọi API để cập nhật trạng thái
          await _updatePaymentTracking(installmentId, newStatus);

          // Cập nhật local state
          setLocalInstallments(prev =>
            prev.map(item => (item.id === installmentId ? { ...item, transactionSuccessful: newStatus } : item)),
          );
        } catch (error) {
          console.error('Error updating payment status:', error);
        }
      },
      onCancel() {},
    });
  };

  const columns: ColumnsType<PaymentInstallment> = [
    {
      title: 'Đợt thanh toán',
      dataIndex: 'installmentName',
      key: 'installmentName',
      width: 125,
    },
    {
      title: 'Ngày đến hạn dự kiến',
      dataIndex: 'dueDate',
      key: 'dueDate',
      width: 150,
    },
    {
      title: 'Giá trị thanh toán (VNĐ)',
      dataIndex: 'amount',
      key: 'amount',
      width: 200,
      render: (amount: number) => formatNumber(amount),
    },
    {
      title: 'Đã thu',
      dataIndex: 'transactionSuccessful',
      key: 'transactionSuccessful',
      width: 60,
      align: 'center',
      render: (transactionSuccessful: boolean, record: PaymentInstallment) => (
        <Checkbox
          checked={transactionSuccessful}
          disabled={transactionSuccessful}
          onChange={e => {
            handleCheckboxChange(record.id, e.target.checked);
          }}
        />
      ),
    },
  ];

  return (
    <div className="payment-tracking">
      <Title level={5}>Theo dõi tiến độ thanh toán</Title>

      <Row>
        <Col span={18}>
          <TableComponent
            queryKeyArr={['get-payment-tracking']}
            className="payment-tracking-table"
            columns={columns}
            dataSource={dataSource}
            rowKey="id"
            isPagination={false}
            footer={() => (
              <div className="payment-summary">
                <Space direction="vertical" size={8} style={{ width: '100%', alignItems: 'flex-end' }}>
                  <Text>
                    Tổng giá trị thanh toán: <span className="amount-text">{formatNumber(calculatedTotalAmount)}</span>
                  </Text>
                  <Text>
                    Tổng giá trị đã thanh toán:{' '}
                    <span className="amount-paid">{formatNumber(calculatedPaidAmount)}</span>
                  </Text>
                </Space>
              </div>
            )}
          />
        </Col>
      </Row>
    </div>
  );
};

export default PaymentTracking;
